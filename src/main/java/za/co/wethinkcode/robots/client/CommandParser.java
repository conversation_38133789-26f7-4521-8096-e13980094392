package za.co.wethinkcode.robots.client;

import com.google.gson.JsonObject;
import com.google.gson.JsonArray;

public class CommandParser {
    private static final String TURN_LEFT = "left";
    private static final String TURN_RIGHT = "right";
    private static final String COMMAND_TURN = "turn";
    private static final String COMMAND_LAUNCH = "launch";
    private static final String COMMAND_FORWARD = "forward";
    private static final String COMMAND_BACK = "back";
    private static final String COMMAND_STATE = "state";
    private static final String COMMAND_LOOK = "look";
    private static final String COMMAND_FIRE = "fire";
    private static final String COMMAND_RELOAD = "reload";
    private static final String COMMAND_REPAIR = "repair";
    private static final String COMMAND_HELP = "help";
    private static final String COMMAND_SAVE = "save";
    private static final String COMMAND_RESTORE = "restore";


    private static final String ROBOT_SNIPER = "sniper";
    private static final String ROBOT_SOLDIER = "soldier";
    private static final String ROBOT_HITBOT = "hitbot";

    public static JsonObject parseCommand(String input, String robotName){
        if (input == null || input.trim().isEmpty()) {
            throw new IllegalArgumentException("Need a command");
        }

        String[] originalParts = input.trim().split(" ");
        String[] parts = input.trim().toLowerCase().split(" ");
        String cmd = parts[0];

        if (cmd.equals(COMMAND_LAUNCH)) {
            return handleLaunch(originalParts);
        } else if (isMovementCommand(cmd)) {
            return handleMovement(parts, robotName);
        } else if (cmd.equals(COMMAND_TURN)) {
            return handleTurn(parts, robotName);
        } else if (isSimpleCommand(cmd)) {
            return makeSimpleCommand(cmd, robotName);
        } else {
            return makeSimpleCommand(cmd, robotName);
        }
    }

    private static boolean isMovementCommand(String command){
        return command.equals(COMMAND_FORWARD) || command.equals(COMMAND_BACK);
    }

    private static boolean isSimpleCommand(String command){
        return switch (command) {
            case COMMAND_STATE, COMMAND_LOOK, COMMAND_FIRE, COMMAND_RELOAD, COMMAND_REPAIR, COMMAND_HELP , COMMAND_SAVE,COMMAND_RESTORE-> true;
            default -> false;
        };
    }

    private static JsonObject handleLaunch(String[] parts){
        if (parts.length != 3) {
            throw new IllegalArgumentException("Need: launch <type> <name>");
        }

        String type = parts[1].toLowerCase();
        String name = parts[2];

        if (!isValidRobotType(type)) {
            throw new IllegalArgumentException("Bad robot type: " + type);
        }

        JsonArray args = new JsonArray();
        args.add(type);
        return buildCommand(COMMAND_LAUNCH, name, args);
    }

    private static boolean isValidRobotType(String type) {
        return type.equals(ROBOT_SNIPER) || type.equals(ROBOT_SOLDIER) || type.equals(ROBOT_HITBOT);
    }

    private static JsonObject handleMovement(String[] parts, String robotName) {
        if (parts.length != 2) {
            throw new IllegalArgumentException("Need: " + parts[0] + " <number>");
        }

        try {
            int steps = Integer.parseInt(parts[1]);
            if (steps <= 0) {
                throw new IllegalArgumentException("Steps must be positive");
            }
            JsonArray args = new JsonArray();
            args.add(steps);
            return buildCommand(parts[0], robotName, args);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Bad number: " + parts[1]);
        }
    }

    private static JsonObject handleTurn(String[] parts, String robotName) {
        if (parts.length != 2) {
            throw new IllegalArgumentException("Need: turn <left|right>");
        }

        String direction = parts[1].toLowerCase();
        if (!direction.equals(TURN_LEFT) && !direction.equals(TURN_RIGHT)) {
            throw new IllegalArgumentException("Turn left or right, not: " + direction);
        }

        JsonArray args = new JsonArray();
        args.add(direction);
        return buildCommand(COMMAND_TURN, robotName, args);
    }

    private static JsonObject makeSimpleCommand(String command, String robotName) {
        return buildCommand(command, robotName, new JsonArray());
    }

    private static JsonObject buildCommand(String command, String robotName, JsonArray arguments) {
        JsonObject cmd = new JsonObject();
        cmd.addProperty("robot", robotName);
        cmd.addProperty("command", command);
        cmd.add("arguments", arguments);
        return cmd;
    }
}
