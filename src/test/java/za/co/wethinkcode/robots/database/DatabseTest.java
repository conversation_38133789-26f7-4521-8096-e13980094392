
package za.co.wethinkcode.robots.database;

import org.junit.jupiter.api.*;
import org.mockito.*;
import za.co.wethinkcode.robots.Database.SaveDatabase;
import za.co.wethinkcode.robots.command.SaveCommand;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.world.World;

import java.sql.*;
        import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
        import static org.mockito.Mockito.*;

class DatabaseTest {
    @Mock
    private Connection mockConnection;

    @Mock
    private PreparedStatement mockPreparedStatement;

    @Mock
    private ResultSet mockResultSet;

    @Mock
    private MultiServers mockServer;

    @Mock
    private SaveCommand mockSaveCommand;

    @InjectMocks
    private SaveDatabase saveDatabase;

    @BeforeEach
    void setUp() throws SQLException {
        MockitoAnnotations.openMocks(this);
        when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
        when(mockConnection.prepareStatement(anyString(), anyInt())).thenReturn(mockPreparedStatement);
        when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
        when(mockPreparedStatement.getGeneratedKeys()).thenReturn(mockResultSet);
    }

    @AfterEach
    void tearDown() throws SQLException {
//        verifyNoMoreInteractions(mockConnection, mockPreparedStatement, mockResultSet);
    }

    @Test
    void testCreateData_Success() throws SQLException {
        // Setup
        when(mockServer.getHeight()).thenReturn(10);
        when(mockServer.getWidth()).thenReturn(10);
        when(mockSaveCommand.getWorldName()).thenReturn("test-world");
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getInt(1)).thenReturn(1);

        // Execute
        int worldId = saveDatabase.createData(mockConnection);

        // Verify
        assertEquals(1, worldId);
        verify(mockPreparedStatement).setInt(1, 10);
        verify(mockPreparedStatement).setInt(2, 10);
        verify(mockPreparedStatement).setString(3, "test-world");
        verify(mockPreparedStatement).executeUpdate();
        verify(mockResultSet).next();
        verify(mockResultSet).getInt(1);
    }

    @Test
    void testCreateData_Failure() throws SQLException {
        // Setup
        when(mockServer.getHeight()).thenReturn(10);
        when(mockServer.getWidth()).thenReturn(10);
        when(mockSaveCommand.getWorldName()).thenReturn("test-world");
        when(mockResultSet.next()).thenReturn(false);

        // Execute & Verify
        assertThrows(SQLException.class, () -> saveDatabase.createData(mockConnection));
    }

//    @Test
//    void testObstacleData_WithObstacles() throws SQLException {
//        // Setup
//        when(mockServer.getObstacle()).thenReturn("1,2;3,4");
//        World mockWorld = mock(World.class);
//
//        // Execute
//        saveDatabase.obstacleData(mockConnection, mockWorld, 1);
//
//        // Verify
//        verify(mockPreparedStatement).setString(1, "1,2");
//        verify(mockPreparedStatement).setInt(2, 1);
//        verify(mockPreparedStatement).setString(1, "3,4");
//        verify(mockPreparedStatement).setInt(2, 1);
//        verify(mockPreparedStatement, times(2)).executeUpdate();
//    }

    @Test
    void testObstacleData_NoObstacles() throws SQLException {
        // Setup
        when(mockServer.getObstacle()).thenReturn("none");
        World mockWorld = mock(World.class);

        // Execute
        saveDatabase.obstacleData(mockConnection, mockWorld, 1);

        // Verify
        verify(mockPreparedStatement, never()).executeUpdate();
    }

//    @Test
//    void testRestoreWorld_Success() throws SQLException {
//        // Setup
//        String worldName = "test-world";
//        when(mockResultSet.next()).thenReturn(true, true, false); // For world query and obstacle query
//        when(mockResultSet.getInt("height")).thenReturn(10);
//        when(mockResultSet.getInt("width")).thenReturn(10);
//        when(mockResultSet.getString("position")).thenReturn("1,2;3,4");
//
//        // Execute
//        World restoredWorld = saveDatabase.restoreWorld(mockConnection, worldName);
//
//        // Verify
//        assertNotNull(restoredWorld);
//        verify(mockPreparedStatement).setString(1, worldName);
//        verify(mockResultSet, times(2)).getString(1,worldName); // For world and obstacles
//    }

    @Test
    void testRestoreWorld_NotFound() throws SQLException {
        // Setup
        String worldName = "non-existent";
        when(mockResultSet.next()).thenReturn(false); // World not found

        // Execute
        World restoredWorld = saveDatabase.restoreWorld(mockConnection, worldName);

        // Verify
        assertNull(restoredWorld);
    }

    @Test
    void testIfNameExists_True() throws SQLException {
        // Setup
        String worldName = "existing-world";
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getInt(1)).thenReturn(1);

        // Execute
        boolean exists = saveDatabase.ifNameExists(mockConnection, worldName);

        // Verify
        assertTrue(exists);
        verify(mockPreparedStatement).setString(1, worldName);
    }

    @Test
    void testIfNameExists_False() throws SQLException {
        // Setup
        String worldName = "non-existent";
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getInt(1)).thenReturn(0);

        // Execute
        boolean exists = saveDatabase.ifNameExists(mockConnection, worldName);

        // Verify
        assertFalse(exists);
    }

//    @Test
//    void testDeleteWorldByName_Success() throws SQLException {
//        // Setup
//        String worldName = "world-to-delete";
//        when(mockPreparedStatement.executeUpdate()).thenReturn(1);
//
//        // Execute
//        saveDatabase.deleteWorldByName(worldName);
//
//        // Verify
//        verify(mockPreparedStatement).setString(1, worldName);
//        verify(mockPreparedStatement).executeUpdate();
//    }

    @Test
    void testGetConnection_Success() throws SQLException {
        // Setup
        SaveDatabase db = new SaveDatabase();

        // This would actually test with a real in-memory DB
        try (Connection conn = db.getConnection()) {
            assertNotNull(conn);
        }
    }
}

//    @Nested
//    class IntegrationTests {
//        private static final String TEST_DB_URL = "************************************************";
//        private Connection realConnection;
//        private SaveDatabase db;
//
//        @BeforeEach
//        void setUp() throws SQLException {
//            realConnection = DriverManager.getConnection(TEST_DB_URL);
//            db = new SaveDatabase();
//
//            // Create test tables
//            try (Statement stmt = realConnection.createStatement()) {
//                stmt.execute("CREATE TABLE world (id INTEGER PRIMARY KEY, height INTEGER, width INTEGER, name TEXT)");
//                stmt.execute("CREATE TABLE obstacles (id INTEGER PRIMARY KEY, position TEXT, worldID INTEGER)");
//            }
//        }

//        @AfterEach
//        void tearDown() throws SQLException {
//            try (Statement stmt = realConnection.createStatement()) {
//                stmt.execute("DROP TABLE world");
//                stmt.execute("DROP TABLE obstacles");
//            }
//            realConnection.close();
//        }

//        @Test
//        void testFullWorkflow() throws SQLException {
//            // Setup test data
//            db.server = mockServer;
//            db.save = mockSaveCommand;
//            when(mockServer.getHeight()).thenReturn(10);
//            when(mockServer.getWidth()).thenReturn(10);
//            when(mockServer.getObstacle()).thenReturn("1,2;3,4");
//            when(mockSaveCommand.getWorldName()).thenReturn("test-world");
//
//            // Save a world
//            int worldId = db.createData(realConnection);
//            assertTrue(worldId > 0);
//
//            // Save obstacles
//            db.obstacleData(realConnection, new World(false), worldId);
//
//            // Check if exists
//            assertTrue(db.ifNameExists(realConnection, "test-world"));
//
//            // Restore world
//            World restored = db.restoreWorld(realConnection, "test-world");
//            assertNotNull(restored);
//            assertEquals(10, restored.getWorldheight());
//            assertEquals(10, restored.getWorldwidth());
//
//            // Verify obstacles were loaded
//            List<Obstacle> obstacles = restored.getObstacles();
//            assertEquals(1, obstacles.size());
//            Obstacle obs = obstacles.get(0);
//            assertEquals(1, obs.getTopLeft().getX());
//            assertEquals(2, obs.getTopLeft().getY());
//            assertEquals(3, obs.getBottomRight().getX());
//            assertEquals(4, obs.getBottomRight().getY());
//
//            // Delete world
//            db.deleteWorldByName("test-world");
//            assertFalse(db.ifNameExists(realConnection, "test-world"));
//        }
//    }
//}
