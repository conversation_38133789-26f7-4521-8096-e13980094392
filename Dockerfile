# Use Eclipse Temurin 21 JRE base image
FROM eclipse-temurin:21-jre

# Install git to satisfy flow library requirements
RUN apt-get update && apt-get install -y git && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Create user and group first
RUN groupadd -r robotworld && useradd -r -g robotworld robotworld

# Copy the built JAR (based on Makefile release output)
COPY target/robot-world-*-jar-with-dependencies.jar app.jar

# Change ownership first
RUN chown -R robotworld:robotworld /app

# Switch to robotworld user to initialize git repository
USER robotworld

# Initialize a minimal git repository to satisfy flow library as robotworld user
RUN git init && \
    git config user.email "<EMAIL>" && \
    git config user.name "Docker Container"


# User is already switched above

# Expose the port (5000 based on test commands)
EXPOSE 5000

# Run the application with working directory explicitly set
ENTRYPOINT ["sh", "-c", "cd /app && java -Duser.dir=/app -jar app.jar -p 5000"]